<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title><?php echo isset($pageTitle) ? $pageTitle : 'Billboard Designer - Borges Media'; ?></title>

    <!-- Google Fonts - Popular and Accessible Font Choices -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Core Google Fonts - Basic Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Display & Decorative Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Mouse+Memoirs&family=Domine:wght@400;500;600;700&family=Baloo+Tamma+2:wght@400;500;600;700;800&family=Courgette&family=Oswald:wght@300;400;500;600;700&family=Kaushan+Script&family=Alfa+Slab+One&family=Yellowtail&family=Paytone+One&display=swap" rel="stylesheet">

    <!-- Script & Handwriting Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&family=Dancing+Script:wght@400;500;600;700&family=Anton&family=Luckiest+Guy&family=Permanent+Marker&family=Coda:wght@400;800&family=Arvo:wght@400;700&family=Lobster&display=swap" rel="stylesheet">

    <!-- Font Loading Verification Script -->
    <script>
        // Ensure fonts are loaded before use
        document.addEventListener('DOMContentLoaded', function() {
            if (document.fonts && document.fonts.ready) {
                document.fonts.ready.then(function() {
                    console.log('✅ All fonts loaded successfully');
                    document.body.classList.add('fonts-loaded');
                });
            }

            // Fallback for older browsers
            setTimeout(function() {
                document.body.classList.add('fonts-loaded');
            }, 3000);
        });
    </script>

    <!-- Additional Professional Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Merriweather:wght@300;400;700&family=Source+Sans+Pro:wght@300;400;600;700&family=Nunito:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&family=Pacifico&display=swap" rel="stylesheet">

    <!-- Font Awesome 6 - For Text Alignment Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Custom Font Styles -->
    <link rel="stylesheet" href="../shared/fonts.css">

    <!-- Shared Header Styles -->
    <style>
        /* Shared Header Styles */
        .bm-fixed-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #28a745 0%, #ff8c00 100%);
            color: white;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 0;
        }

        .bm-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .bm-header-brand {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .bm-header-logo {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .bm-header-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .bm-header-subtitle {
            font-size: 0.9rem;
            margin: 2px 0 0 0;
            opacity: 0.9;
            font-weight: 400;
        }

        .bm-header-nav {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .bm-btn-back {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .bm-btn-back:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        .bm-btn-back:active {
            transform: translateY(0);
        }

        /* Body padding to account for fixed header */
        body {
            padding-top: 80px;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .bm-header-content {
                padding: 12px 15px;
            }

            .bm-header-title {
                font-size: 1.2rem;
            }

            .bm-header-subtitle {
                font-size: 0.8rem;
            }

            .bm-header-logo {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .bm-btn-back {
                padding: 8px 12px;
                font-size: 0.8rem;
            }

            body {
                padding-top: 70px;
            }
        }

        @media (max-width: 480px) {
            .bm-header-brand {
                gap: 10px;
            }

            .bm-header-title {
                font-size: 1.1rem;
            }

            .bm-header-subtitle {
                display: none;
            }

            .bm-btn-back span.btn-text {
                display: none;
            }
        }
    </style>

    <?php
    // Include additional CSS files if specified
    if (isset($additionalCSS) && is_array($additionalCSS)) {
        foreach ($additionalCSS as $cssFile) {
            echo '<link rel="stylesheet" href="' . htmlspecialchars($cssFile) . '">' . "\n    ";
        }
    }
    ?>
</head>

<body>
    <!-- Fixed Header with Back Button -->
    <div class="bm-fixed-header">
        <div class="bm-header-content">
            <div class="bm-header-brand">
                <div class="bm-header-logo">BM</div>
                <div>
                    <h1 class="bm-header-title">
                        <?php if (isset($headerIcon)): ?>
                            <i class="<?php echo htmlspecialchars($headerIcon); ?>"></i>
                        <?php endif; ?>
                        <?php echo isset($headerTitle) ? htmlspecialchars($headerTitle) : 'Billboard Designer'; ?>
                    </h1>
                    <?php if (isset($headerSubtitle)): ?>
                        <p class="bm-header-subtitle"><?php echo htmlspecialchars($headerSubtitle); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="bm-header-nav">
                <button onclick="goBack()" class="bm-btn-back">
                    <i class="fas fa-arrow-left"></i>
                    <span class="btn-text">Back to Home</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Font Manager -->
    <script src="../shared/font-manager.js"></script>

    <script>
        // Back button functionality
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // Fallback - redirect to parent directory
                window.location.href = '../';
            }
        }
    </script>
