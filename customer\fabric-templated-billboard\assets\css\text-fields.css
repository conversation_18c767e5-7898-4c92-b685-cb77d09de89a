/* ========================================
   TEXT FIELDS AND CUSTOMIZATION
   ======================================== */

/* Text Fields Container */
.text-fields-container {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
}

.text-field-group {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    background: white;
    transition: var(--transition);
}

.text-field-group:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.text-field-group label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: #495057;
    font-size: 14px;
}

.text-field-group input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.text-field-group input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.text-controls {
    margin-top: 10px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.customize-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.customize-btn:hover {
    background: #0056b3;
}

/* Inline Customize Button Styles */
.customize-btn-inline {
    background: #007bff !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.2) !important;
}

.customize-btn-inline:hover {
    background: #0056b3 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3) !important;
}

.customize-btn-inline:active {
    transform: scale(0.98) !important;
}

.customize-btn-inline:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Text field with inline button container */
.text-field-group > div {
    display: flex !important;
    gap: 8px !important;
    align-items: center !important;
    width: 100% !important;
}

.text-field-group input[type="text"] {
    flex: 1 !important;
    min-width: 0 !important;
}

/* ========================================
   DESKTOP LEFT-SIDE POSITIONING
   ======================================== */

/* Desktop positioning for Text Fields Panel (1024px and above only) */
@media (min-width: 1024px) {
    #textEditingSection {
        position: fixed;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        width: 350px;
        max-height: calc(100vh - 120px);
        background: white;
        border-radius: var(--border-radius);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        overflow-y: auto;
        border: 1px solid #e0e0e0;
    }

    #textEditingSection h3 {
        position: sticky;
        top: 0;
        background: var(--primary-color);
        color: white;
        margin: 0;
        padding: var(--spacing-md);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        font-size: 1.1rem;
        z-index: 1001;
    }

    #textEditingSection .text-fields-container {
        margin: 0;
        border: none;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        background: white;
    }

    /* Ensure proper scrolling for content */
    #textEditingSection::-webkit-scrollbar {
        width: 8px;
    }

    #textEditingSection::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    #textEditingSection::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
        transition: background 0.2s ease;
    }

    #textEditingSection::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
    }
}
